package main

import (
	"context"

	"github.com/cloudwego/eino/components/tool"
	"github.com/cloudwego/eino/components/tool/utils"
	"github.com/cloudwego/eino/compose"
	"github.com/cloudwego/eino/schema"
)

func AddTodoFunc(_ context.Context, _ string) (string, error) {
	return `{"msg": "add todo success"}`, nil
}

func getAddTodoTool() tool.InvokableTool {
	info := &schema.ToolInfo{
		Name: "add_todo",
		Desc: "Add a todo item",
		ParamsOneOf: schema.NewParamsOneOfByParams(map[string]*schema.ParameterInfo{
			"content": {
				Desc:     "The content of the todo item",
				Type:     schema.String,
				Required: true,
			},
			"started_at": {
				Desc: "The started time of the todo item, in unix timestamp",
				Type: schema.Integer,
			},
			"deadline": {
				Desc: "The deadline of the todo item, in unix timestamp",
				Type: schema.Integer,
			},
		}),
	}

	return utils.NewTool(info, AddTodoFunc)
}

func toolDemo() {
	tool := getAddTodoTool()

	tools := []tool.BaseTool{}
	conf := &compose.ToolsNodeConfig{
		Tools: []tool.BaseTool{tool}, // 工具可以是 InvokableTool 或 StreamableTool
	}
	// toolsNode, err := compose.NewToolNode(context.Background(), conf)
}
