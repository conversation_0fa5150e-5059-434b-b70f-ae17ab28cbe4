package main

import (
	"context"
	"fmt"
	"io"
	"log"
	"os"

	"github.com/cloudwego/eino-ext/components/model/openai"
	"github.com/cloudwego/eino/components/prompt"
	"github.com/cloudwego/eino/schema"
)

func llmDemo() {
	template := prompt.FromMessages(schema.FString,
		schema.SystemMessage("你是一个{role}。你需要用{style}的语气回答问题。你的目标是帮助程序员保持积极乐观的心态，提供技术建议的同时也要关注他们的心理健康。"),
		schema.MessagesPlaceholder("chat_history", true),
		schema.UserMessage("问题: {question}"),
	)
	messages, err := template.Format(context.Background(), map[string]any{
		"role":     "程序员鼓励师",
		"style":    "积极、温暖且专业",
		"question": "我的代码一直报错，感觉好沮丧，怎么办？",
		"chat_history": []*schema.Message{
			schema.UserMessage("你好"),
			schema.AssistantMessage("嘿，我是你的程序员鼓励师！记住，每个优秀的程序员都是从 Debug 中成长起来的。有什么我可以帮你的吗？", nil),
			schema.UserMessage("我觉得自己写的代码太烂了"),
			schema.AssistantMessage("每个程序员都经历过这个阶段！重要的是你在不断学习和进步。让我们一起看看代码，我相信通过重构和优化，它会变得更好。记住，Rome wasn't built in a day，代码质量是通过持续改进来提升的。", nil),
		},
	})

	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}

	chatModel, err := openai.NewChatModel(context.Background(), &openai.ChatModelConfig{
		Model:   "deepseek-ai/DeepSeek-V3",
		APIKey:  os.Getenv("SILICONFLOW_API_KEY"),
		BaseURL: "https://api.siliconflow.cn/v1",
	})

	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}

	result, err := chatModel.Generate(context.Background(), messages)
	fmt.Println(err)
	fmt.Println(result)

	streamResult, _ := chatModel.Stream(context.Background(), messages)

	reportStream(streamResult)
}

func reportStream(sr *schema.StreamReader[*schema.Message]) {
	defer sr.Close()

	i := 0
	for {
		message, err := sr.Recv()
		if err == io.EOF { // 流式输出结束
			return
		}
		if err != nil {
			log.Fatalf("recv failed: %v", err)
		}
		fmt.Print(message.Content)
		i++
	}
}
